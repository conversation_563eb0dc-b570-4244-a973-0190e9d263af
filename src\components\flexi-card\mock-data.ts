import { CardType } from './type';
/* eslint-disable sonarjs/no-duplicate-string */
export const mockCards = [
  {
    id: '1',
    cardNumber: '4246 0511 0135 9627',
    cardHolderName: '<PERSON>',
    expiryDate: '07/2028',
    cvv: '685',
    balance: 85.87,
    currency: 'USD',
    cardType: 'USD Zero',
    status: 'ACTIVE',
    brand: 'FLEXI',
  },
  {
    id: '2',
    cardNumber: '4246 0511 0135 8888',
    cardHolderName: '<PERSON>',
    expiryDate: '05/2026',
    cvv: '123',
    balance: 120.50,
    currency: 'EUR',
    cardType: 'EUR Basic',
    status: 'ACTIVE',
    brand: 'FLEXI',
  },
  {
    id: '3',
    cardNumber: '0000 1254 02158 9999',
    cardHolderName: 'John Do<PERSON> 2',
    expiryDate: '05/2076',
    cvv: '123',
    balance: 20.50,
    currency: 'EUR',
    cardType: 'EUR Basic',
    status: 'ACTIVE',
    brand: 'FLEXI',
  },
];
export const mockTransactions = [
  {
    id: '1',
    description: 'Noon 80038888 AE',
    amount: -12.33,
    date: '07-26 21:28',
    time: '21:28:21',
    type: 'purchase',
    status: 'pending',
    activity: 'Transaction',
    card: 'USD Zero ••9627',
    generalTxnFee: 0,
    txnRef: '20250726132821194909947491929702',
    descriptor: 'Noon 80038888 AE',
  },
  {
    id: '2',
    description: 'Other',
    amount: 0,
    date: '07-26 21:28',
    time: '21:28:21',
    type: 'other',
    status: 'completed',
    activity: 'Card Maintenance',
    card: 'USD Zero ••9627',
    netAmountCredited: 0,
    generalTxnFee: 0,
    txnRef: '20250726140003194910745146209075',
  },
  {
    id: '3',
    description: 'Top Up',
    amount: 100,
    date: '07-26 21:20',
    time: '21:19:58',
    type: 'topup',
    status: 'completed',
    activity: 'Card Maintenance',
    card: 'USD Zero ••9627',
    netAmountCredited: 100,
    generalTxnFee: 0,
    txnRef: '20250726132004194909738997272166',
  },
  {
    id: '4',
    description: 'Top Up Fee',
    amount: -1.8,
    date: '07-26 21:19',
    time: '21:19:58',
    type: 'fee',
    status: 'completed',
    activity: 'Card Maintenance',
    card: 'USD Zero ••9627',
    netAmountCredited: 1.8,
    generalTxnFee: 0,
    txnRef: '20250726132004194909738997272166',
  },
  {
    id: '5',
    description: 'Card Opening Fee',
    amount: 0,
    date: '07-26 21:19',
    time: '21:19:58',
    type: 'fee',
    status: 'completed',
    activity: 'Card Maintenance',
    card: 'USD Zero ••9627',
    netAmountCredited: 0,
    generalTxnFee: 0,
    txnRef: '20250726131958194909736475656601',
  },
];

export const cardTypes: CardType[] = [
  {
    id: 'usd-premium',
    name: 'USD Premium',
    currency: 'USD',
    activationFee: 100,
    activationFeeCurrency: 'USD',
    initialTopUp: 500,
    initialTopUpCurrency: 'USD',
    topUpFee: '1.8%',
    monthlyFee: 'Free',
    generalTxnFee: 'Free',
    foreignTxnFee: 'Free',
    available: false,
    comingSoon: true,
  },
  {
    id: 'usd-zero',
    name: 'USD Zero',
    currency: 'USD',
    activationFee: 0,
    activationFeeCurrency: 'USDT',
    initialTopUp: 100,
    initialTopUpCurrency: 'USDT',
    topUpFee: '1.8%',
    monthlyFee: 'Free',
    generalTxnFee: 'Free',
    foreignTxnFee: '1.5%',
    available: true,
  },
  {
    id: 'usd-basic',
    name: 'USD Basic',
    currency: 'USD',
    activationFee: 8,
    activationFeeCurrency: 'USDT',
    initialTopUp: 100,
    initialTopUpCurrency: 'USDT',
    topUpFee: '1.8%',
    monthlyFee: 'Free',
    generalTxnFee: 'Free',
    foreignTxnFee: '1.5%',
    available: true,
  },
  {
    id: 'usd-pro',
    name: 'USD Pro',
    currency: 'USD',
    activationFee: 12,
    activationFeeCurrency: 'USDT',
    initialTopUp: 100,
    initialTopUpCurrency: 'USDT',
    topUpFee: '1.9%',
    monthlyFee: 'Free',
    generalTxnFee: '1.2%',
    foreignTxnFee: '1.5%',
    available: true,
  },
];
