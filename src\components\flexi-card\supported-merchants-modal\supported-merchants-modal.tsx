import {
  Modal,
  Stack,
  Group,
  Text,
  TextInput,
  Box,
  Avatar,
  ScrollArea,
  useMantineTheme,
  rem,
  Alert,
  createStyles,
} from '@mantine/core';
import { IconSearch, IconInfoCircle } from '@tabler/icons-react';
import { useState } from 'react';

interface SupportedMerchantsModalProps {
  opened: boolean;
  onClose: () => void;
}

// Mock merchant data based on the image
const mockMerchants = [
  {
    id: '1',
    name: 'Facebook',
    logo: '',
    backgroundColor: '#1877F2',
  },
  {
    id: '2',
    name: 'Amazon',
    logo: '',
    backgroundColor: '#FF9900',
  },
  {
    id: '3',
    name: 'Kickstarter',
    logo: '',
    backgroundColor: '#05CE78',
  },
  {
    id: '4',
    name: 'Ring4',
    logo: '',
    backgroundColor: '#4A90E2',
  },
  {
    id: '5',
    name: 'OpenAI',
    logo: '',
    backgroundColor: '#000000',
  },
  {
    id: '6',
    name: 'Shopify',
    logo: '',
    backgroundColor: '#96BF48',
  },
  {
    id: '7',
    name: 'Netflix',
    logo: '',
    backgroundColor: '#E50914',
  },
  {
    id: '8',
    name: 'Spotify',
    logo: '',
    backgroundColor: '#1DB954',
  },
  {
    id: '9',
    name: 'Adobe',
    logo: '',
    backgroundColor: '#FF0000',
  },
  {
    id: '10',
    name: 'Microsoft',
    logo: '',
    backgroundColor: '#00BCF2',
  },
  {
    id: '11',
    name: 'Google',
    logo: '',
    backgroundColor: '#4285F4',
  },
  {
    id: '12',
    name: 'Apple',
    logo: '',
    backgroundColor: '#000000',
  },
];

// eslint-disable-next-line complexity, sonarjs/cognitive-complexity
const useStyles = createStyles((theme) => ({
  modal: {
    '.mantine-Modal-content': {
      background: theme.colorScheme === 'dark'
        ? `linear-gradient(145deg, ${theme.colors.dark[7]}, ${theme.colors.dark[6]})`
        : `linear-gradient(145deg, ${theme.white}, #f8f9fa)`,
      border: theme.colorScheme === 'dark'
        ? `1px solid ${theme.colors.dark[4]}`
        : `1px solid ${theme.colors.gray[2]}`,
      boxShadow: theme.colorScheme === 'dark'
        ? '0 25px 50px -12px rgba(0, 0, 0, 0.5)'
        : '0 25px 50px -12px rgba(0, 0, 0, 0.25)',
    },
    '.mantine-Modal-header': {
      backgroundColor: 'transparent',
      borderBottom: `1px solid ${theme.colorScheme === 'dark' ? theme.colors.dark[4] : theme.colors.gray[2]}`,
      paddingBottom: theme.spacing.md,
    },
    '.mantine-Modal-title': {
      fontSize: rem(20),
      fontWeight: 600,
      color: theme.colorScheme === 'dark' ? theme.white : theme.colors.gray[8],
    },
  },

  alertCard: {
    background: theme.colorScheme === 'dark'
      ? `linear-gradient(135deg, ${theme.colors.blue[9]}, ${theme.colors.blue[8]})`
      : `linear-gradient(135deg, ${theme.colors.blue[0]}, ${theme.colors.blue[1]})`,
    border: `1px solid ${theme.colorScheme === 'dark' ? theme.colors.blue[7] : theme.colors.blue[2]}`,
    borderRadius: theme.radius.lg,
  },

  searchInput: {
    '.mantine-TextInput-input': {
      backgroundColor: theme.colorScheme === 'dark' ? theme.colors.dark[6] : theme.colors.gray[0],
      border: `1px solid ${theme.colorScheme === 'dark' ? theme.colors.dark[4] : theme.colors.gray[3]}`,
      borderRadius: theme.radius.lg,
      padding: `${rem(12)} ${rem(16)}`,
      fontSize: rem(14),
      '&:focus': {
        borderColor: theme.colorScheme === 'dark' ? theme.colors.blue[6] : theme.colors.blue[5],
        boxShadow: `0 0 0 2px ${theme.colorScheme === 'dark' ? theme.colors.blue[9] : theme.colors.blue[1]}`,
      },
      '&::placeholder': {
        color: theme.colorScheme === 'dark' ? theme.colors.dark[2] : theme.colors.gray[5],
      },
    },
  },

  merchantItem: {
    display: 'flex',
    alignItems: 'center',
    padding: `${rem(12)} ${rem(16)}`,
    borderRadius: theme.radius.md,
    cursor: 'pointer',
    transition: 'background-color 150ms ease',
    '&:hover': {
      backgroundColor: theme.colorScheme === 'dark' ? theme.colors.dark[5] : theme.colors.gray[0],
    },
  },

  merchantAvatar: {
    borderRadius: theme.radius.sm,
    border: `1px solid ${theme.colorScheme === 'dark' ? theme.colors.dark[4] : theme.colors.gray[2]}`,
  },

  closeButton: {
    border: 'none',
    backgroundColor: theme.colorScheme === 'dark' ? theme.colors.dark[5] : '#f8f9fa',
    borderRadius: '50%',
    width: rem(32),
    height: rem(32),
    color: theme.colorScheme === 'dark' ? theme.white : '#495057',
    '&:hover': {
      backgroundColor: theme.colorScheme === 'dark' ? theme.colors.dark[4] : '#e9ecef',
    },
  },
}));

export function SupportedMerchantsModal({ opened, onClose }: SupportedMerchantsModalProps) {
  const [searchValue, setSearchValue] = useState('');
  const theme = useMantineTheme();
  const { classes } = useStyles();

  const filteredMerchants = mockMerchants.filter((merchant) => merchant.name.toLowerCase().includes(searchValue.toLowerCase()));

  return (
    <Modal
      opened={opened}
      onClose={onClose}
      title="Supported merchants"
      centered
      size="md"
      radius="xl"
      scrollAreaComponent={ScrollArea.Autosize}
      styles={{
        content: {
          background: theme.colorScheme === 'dark'
            ? `linear-gradient(145deg, ${theme.colors.dark[7]}, ${theme.colors.dark[6]})`
            : `linear-gradient(145deg, ${theme.white}, #f8f9fa)`,
          border: theme.colorScheme === 'dark'
            ? `1px solid ${theme.colors.dark[4]}`
            : `1px solid ${theme.colors.gray[2]}`,
          boxShadow: theme.colorScheme === 'dark'
            ? '0 25px 50px -12px rgba(0, 0, 0, 0.5)'
            : '0 25px 50px -12px rgba(0, 0, 0, 0.25)',
        },
        header: {
          backgroundColor: 'transparent',
          paddingBottom: theme.spacing.md,
        },
        title: {
          fontSize: rem(20),
          fontWeight: 600,
          color: theme.colorScheme === 'dark' ? theme.white : theme.colors.gray[8],
        },
        close: {
          border: 'none',
          backgroundColor: theme.colorScheme === 'dark' ? theme.colors.dark[5] : '#f8f9fa',
          borderRadius: '50%',
          width: rem(32),
          height: rem(32),
          color: theme.colorScheme === 'dark' ? theme.white : '#495057',
          '&:hover': {
            backgroundColor: theme.colorScheme === 'dark' ? theme.colors.dark[4] : '#e9ecef',
          },
        },
      }}
    >
      <Stack spacing="lg">
        {/* Warning Alert */}
        <Alert
          icon={<IconInfoCircle size={16} />}
          color="blue"
          variant="light"
          className={classes.alertCard}
        >
          <Text size="sm">
            Payment processors update their policies constantly, support for a certain merchant is not guaranteed - even if it is listed here.
          </Text>
        </Alert>

        {/* Search Input */}
        <TextInput
          placeholder="Merchant name"
          value={searchValue}
          onChange={(event) => setSearchValue(event.currentTarget.value)}
          icon={<IconSearch size={16} color={theme.colors.gray[5]} />}
          className={classes.searchInput}
        />

        {/* Merchants List */}
        <ScrollArea.Autosize mah={400}>
          <Stack spacing={0}>
            {filteredMerchants.map((merchant) => (
              <Box key={merchant.id} className={classes.merchantItem}>
                <Group spacing="md" w="100%">
                  <Avatar
                    size={40}
                    radius="sm"
                    className={classes.merchantAvatar}
                    style={{
                      backgroundColor: merchant.backgroundColor,
                      color: 'white',
                      fontWeight: 600,
                    }}
                  >
                    {merchant.name.charAt(0)}
                  </Avatar>
                  <Text size="md" weight={500}>
                    {merchant.name}
                  </Text>
                </Group>
              </Box>
            ))}

            {filteredMerchants.length === 0 && (
              <Box p="md" ta="center">
                <Text size="sm" color="dimmed">
                  No merchants found matching &quot;
                  {searchValue}
                  &quot;
                </Text>
              </Box>
            )}
          </Stack>
        </ScrollArea.Autosize>
      </Stack>
    </Modal>
  );
}
