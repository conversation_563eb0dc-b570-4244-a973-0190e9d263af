import {
  Mo<PERSON>, <PERSON>ack, Group, Text, Button, NumberInput, Select, Divider, Box,
  ScrollArea,
  useMantineTheme,
  rem,
  Tooltip,
  Paper,
} from '@mantine/core';
import { useState } from 'react';
import { IconInfoCircle } from '@tabler/icons-react';
import useTranslation from 'next-translate/useTranslation';

interface TopUpModalProps {
  opened: boolean;
  onClose: () => void;
  card: {
    id: string;
    cardNumber: string;
    cardHolderName: string;
    balance: number;
    currency: string;
    cardType: string;
  };
}

export function TopUpModal({ opened, onClose, card }: TopUpModalProps) {
  const { t } = useTranslation();
  const [amount, setAmount] = useState<number>(0);
  const [selectedCurrency, setSelectedCurrency] = useState('USDT');
  const theme = useMantineTheme();
  const isDark = theme.colorScheme === 'dark';

  const presetAmounts = [15, 50, 100];
  const feePercentage = 1.8;
  const fee = amount > 0 ? (amount * feePercentage) / 100 : 0;
  const netAmount = amount > 0 ? amount - fee : 0;
  const accountBalance = 14.********; // Mock USDT balance

  const handlePresetAmount = (presetAmount: number) => {
    setAmount(presetAmount);
  };

  const handleTopUp = () => {
    onClose();
  };

  return (
    <Modal
      opened={opened}
      onClose={onClose}
      scrollAreaComponent={ScrollArea.Autosize}
      title={t('common:topUp')}
      size="md"
      centered
      withCloseButton
      radius={30}
      styles={{
        close: {
          border: 'none',
          backgroundColor:
              theme.colorScheme === 'dark'
                ? theme.colors.dark[5]
                : '#f8f9fa',
          borderRadius: '50%',
          width: rem(32),
          height: rem(32),
          color:
              theme.colorScheme === 'dark'
                ? theme.white
                : '#495057',
          '&:hover': {
            backgroundColor:
                theme.colorScheme === 'dark'
                  ? theme.colors.dark[4]
                  : '#e9ecef',
          },
        },
      }}
    >
      <Stack spacing="lg">
        {/* Card Info Section */}
        <Paper
          radius="md"
          p={rem(8)}
          bg={isDark ? '#25262B' : '#f8f9fa'}
          style={{ minWidth: rem(280) }}
        >
          {/* main horizontal layout */}
          <Group position="apart" align="flex-start">
            {/* left column ---------------------------------------------------- */}
            <Box>
              {/* VISA pill + label */}
              <Group align="center" mb={rem(4)}>
                <Box
                  px={rem(5)}
                  py={rem(2)}
                  sx={() => ({
                    backgroundColor: theme.black,
                    borderRadius: theme.radius.xs,
                  })}
                >
                  <Text size={rem(10)} weight={600} color="white">
                    VISA
                  </Text>
                </Box>

                <Text size={rem(13)} weight={500} c="dimmed">
                  USD Zero
                </Text>
              </Group>

              {/* masked card number */}
              <Text size={rem(13)} c="dimmed">
                ••9627
              </Text>
            </Box>

            {/* right column --------------------------------------------------- */}
            <Box ta="right">
              <Text size={rem(13)} c="dimmed" mb={rem(4)}>
                Card balance (USD)
              </Text>

              <Text size={rem(24)} weight={600}>
                $
                {' '}
                {card.balance.toFixed(2)}
              </Text>
            </Box>
          </Group>
        </Paper>

        <Divider />

        {/* Top-up Amount Section */}
        <Stack spacing="sm">
          <Group position="apart" align="center">
            <Text size="sm" weight={600}>Top-up amount</Text>
            <Group spacing="xs">
              {presetAmounts.map((preset) => (
                <Button
                  key={preset}
                  variant="light"
                  size="xs"
                  color="green"
                  onClick={() => handlePresetAmount(preset)}
                  sx={{
                    fontSize: '12px',
                    height: '24px',
                    padding: '0 8px',
                  }}
                >
                  $
                  {preset}
                </Button>
              ))}
            </Group>
          </Group>

          {/* Amount Input */}
          <Group align="center" spacing="xs" noWrap>
            <NumberInput
              value={amount || ''}
              onChange={(value) => setAmount(value || 0)}
              placeholder="$0.00"
              size="xl"
              variant="unstyled"
              styles={{
                input: {
                  fontSize: '2rem',
                  fontWeight: 600,
                  textAlign: 'left',
                  padding: 0,
                },
              }}
              min={0}
              step={1}
              precision={2}
            />
            <Text size="xl" weight={600} color="dimmed">USD</Text>
          </Group>
        </Stack>
        <Divider />

        {/* Payment Method */}
        <Stack spacing="sm">
          <Group align="center" spacing="xs" noWrap>
            <Text size="sm" weight={600} sx={{ flex: 1 }}>Pay with</Text>
            <Select
              value={selectedCurrency}
              onChange={(value) => setSelectedCurrency(value || 'USDT')}
              data={[
                { value: 'USDT', label: 'USDT' },
                { value: 'BTC', label: 'BTC' },
                { value: 'ETH', label: 'ETH' },
              ]}
              radius={30}
              rightSection={(
                <Box
                  sx={{
                    width: '20px',
                    height: '20px',
                    borderRadius: '50%',
                    backgroundColor: '#26C281',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    marginRight: '8px',
                  }}
                >
                  <Text size="xs" color="white" weight={700}>T</Text>
                </Box>
            )}
              styles={{
                rightSection: { pointerEvents: 'none' },
              }}
            />
          </Group>
        </Stack>

        <Divider />

        {/* Fee Information */}
        <Stack spacing="xs">
          <Group position="apart" align="center">
            <Group spacing="xs">
              <Text size="sm" color="dimmed">Fee (1.8%)</Text>
              <Tooltip
                label="We charge a 1.8% fee on top‑ups. The fee is calculated in the same token you are using for payment."
                withArrow
                multiline
                width={260}
                position="top"
                styles={{
                  tooltip: {
                    backgroundColor: 'rgba(0, 0, 0, .85)',
                    color: '#fff',
                    borderRadius: '6px',
                    padding: '8px 12px',
                  },
                }}
              >
                <IconInfoCircle
                  size={14}
                  color="#aaa"
                  style={{
                    cursor: 'pointer',
                  }}
                />
              </Tooltip>
            </Group>
            <Text size="sm" color="dimmed">
              {amount > 0 ? `-$${fee.toFixed(2)}` : '-'}
            </Text>
          </Group>

          <Group position="apart" align="center">
            <Text size="sm" color="dimmed">Net credited to the card</Text>
            <Text size="sm" color="dimmed">
              {amount > 0 ? `$${netAmount.toFixed(2)}` : '-'}
            </Text>
          </Group>
        </Stack>

        <Divider />

        {/* Account Balance */}
        <Group position="apart" align="center">
          <Text size="sm" weight={700}>Account balance</Text>
          <Group spacing="xs">
            <Box
              sx={{
                width: '16px',
                height: '16px',
                borderRadius: '50%',
                backgroundColor: '#26C281',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
              }}
            >
              <Text size="xs" color="white" weight={700}>T</Text>
            </Box>
            <Text size="sm" color="green" weight={500}>
              {accountBalance.toFixed(2)}
              {' '}
              USDT
            </Text>
          </Group>
        </Group>

        {/* Action Button */}
        <Button
          fullWidth
          size="md"
          disabled={amount <= 0}
          onClick={handleTopUp}
          color="gray"
          variant="light"
          sx={{
            backgroundColor: amount > 0 ? '#000' : '#f5f5f5',
            color: amount > 0 ? 'white' : '#aaa',
            '&:hover': {
              backgroundColor: amount > 0 ? '#333' : '#f5f5f5',
            },
            '&:disabled': {
              backgroundColor: isDark ? '#25262B' : '#f5f5f5',
              color: '#aaa',
            },
          }}
        >
          {amount > 0 ? `Top Up $${amount.toFixed(2)}` : 'Enter Top-Up Amount'}
        </Button>
      </Stack>
    </Modal>
  );
}
