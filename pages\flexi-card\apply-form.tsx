import { useRouter } from 'next/router';
import {
  Container, Paper, Title, Text,
} from '@mantine/core';
import { Layout } from '@/components/layout/layout';
import { CardApplicationForm } from '@/components/flexi-card/card-application-form/card-application-form';
import { cardTypes } from '@/components/flexi-card/mock-data';

export default function ApplyFormPage() {
  const router = useRouter();
  const { cardId } = router.query;

  const selectedCardType = cardTypes.find((card) => card.id === cardId);

  if (!selectedCardType) {
    return (
      <Layout>
        <Container>
          <Title>Card not found</Title>
          <Text>The requested card type could not be found.</Text>
        </Container>
      </Layout>
    );
  }

  return (
    <Layout>
      <Container size="sm" my="xl">
        <Paper withBorder shadow="md" p="xs" radius="md">
          <CardApplicationForm selectedCardType={selectedCardType} />
        </Paper>
      </Container>
    </Layout>
  );
}
