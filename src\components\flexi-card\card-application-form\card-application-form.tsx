/* eslint-disable no-nested-ternary */
import React, { useState } from 'react';
import {
  Stack,
  Group,
  Text,
  Select,
  Button,
  Box,
  NumberInput,
  useMantineTheme,
  rem,
  Center,
} from '@mantine/core';
import { IconInfoCircle } from '@tabler/icons-react';
import { useRouter } from 'next/router';
import { CardType } from '../type';

interface CardApplicationFormProps {
  selectedCardType: CardType;
}

interface FormData {
  initialTopUp: number;
  paymentMethod: string;
}

export function CardApplicationForm({ selectedCardType }: CardApplicationFormProps) {
  const theme = useMantineTheme();
  const isDark = theme.colorScheme === 'dark';
  const router = useRouter();
  const [formData, setFormData] = useState<FormData>({
    initialTopUp: selectedCardType.initialTopUp,
    paymentMethod: selectedCardType.activationFeeCurrency,
  });

  const { activationFee } = selectedCardType;
  const totalPayable = formData.initialTopUp + activationFee;

  const exchangeRates: Record<string, number> = { USDT: 1, ETH: 0.0003846, SGD: 1.36 };
  const getPaymentAmount = () => (totalPayable * (exchangeRates[formData.paymentMethod] || 1)).toFixed(
    formData.paymentMethod === 'ETH' ? 7 : 2,
  );

  const getCurrencySymbol = (c: string) => (c === 'ETH' ? 'Ξ' : c === 'USDT' ? '₮' : '$');

  const getCurrencyColor = (c: string) => (c === 'USDT' ? '#26A17B' : c === 'ETH' ? '#627EEA' : '#1FC7D4');

  const getAccountBalance = (c: string) => (c === 'USDT' ? '14.91' : c === 'ETH' ? '0.025' : '500.00');

  const handleSubmit = () => {
    // submit logic
  };

  return (
    <Box sx={{
      backgroundColor: isDark ? '#383838ff' : '#F8F9FA',
      padding: theme.spacing.xl,
      borderRadius: theme.radius.lg,
    }}
    >
      <Stack spacing="xl">

        {/* Initial top-up section */}
        <Box>
          <Group position="apart" mb="xs">
            <Group spacing="xs">
              <Text size="sm" weight={700}>Initial top-up</Text>
              <IconInfoCircle size={16} color={theme.colors.gray[5]} />
            </Group>
          </Group>

          <Box
            sx={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'space-between',
              border: '1px solid #E9ECEF',
              borderRadius: 20,
              padding: `${rem(12)} ${rem(16)}`,
              background: isDark ? '#262626ff' : '#fff',
            }}
          >
            <NumberInput
              value={formData.initialTopUp}
              min={100}
              step={10}
              parser={(val) => val?.replace(/\D/g, '') ?? ''}
              formatter={(val) => (val ? `${val}` : '')}
              hideControls
              styles={{
                wrapper: {
                  flex: 1,
                },
                input: {
                  border: 'none',
                  fontSize: rem(16),
                  fontWeight: 500,
                  padding: 0,
                  height: 'auto',
                  minHeight: 'auto',
                  background: 'transparent',
                },
              }}
            />
            <Text size="md" weight={500} ml="md">USD</Text>
          </Box>

          <Text size="xs" color="dimmed" mt="xs">
            Minimum initial top-up is 100 USD.
          </Text>
        </Box>

        {/* Activation fee */}
        <Group position="apart">
          <Text size="sm" color="dimmed">Activation fee</Text>
          <Text size="sm" color="dark">
            {activationFee}
            {' '}
            USD
          </Text>
        </Group>

        {/* Total payable */}
        <Group position="apart">
          <Text size="lg" weight={600}>Total payable now</Text>
          <Text size="lg" weight={600}>
            {totalPayable}
            {' '}
            USD
          </Text>
        </Group>

        {/* Pay with section */}
        <Box>
          <Text size="sm" weight={500} mb="sm">Pay with</Text>

          <Select
            value={formData.paymentMethod}
            onChange={(val) => setFormData((f) => ({ ...f, paymentMethod: val || f.paymentMethod }))}
            data={[
              { value: 'USDT', label: 'USDT' },
              { value: 'ETH', label: 'ETH' },
              { value: 'SGD', label: 'SGD' },
            ]}
            icon={(
              <Box
                sx={{
                  width: rem(20),
                  height: rem(20),
                  borderRadius: '50%',
                  backgroundColor: getCurrencyColor(formData.paymentMethod),
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  color: 'white',
                  fontSize: rem(12),
                  fontWeight: 600,
                }}
              >
                {getCurrencySymbol(formData.paymentMethod)}
              </Box>
            )}
            styles={{
              input: {
                height: rem(44),
                borderRadius: 20,
              },
            }}
          />
          {/* Account balance */}
          <Group position="apart" mt="md">
            <Text size="xs" color="dimmed">Account balance</Text>
            <Group spacing="xs">
              <Box
                sx={{
                  width: rem(16),
                  height: rem(16),
                  borderRadius: '50%',
                  backgroundColor: getCurrencyColor(formData.paymentMethod),
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  color: 'white',
                  fontSize: rem(10),
                  fontWeight: 600,
                }}
              >
                {getCurrencySymbol(formData.paymentMethod)}
              </Box>
              <Text size="sm" weight={500}>
                {getAccountBalance(formData.paymentMethod)}
                {' '}
                {formData.paymentMethod}
              </Text>
            </Group>
          </Group>
        </Box>

        {/* Pay button */}
        <Button
          fullWidth
          onClick={handleSubmit}
          size="lg"
          radius="md"
          sx={{
            backgroundColor: '#C7F453',
            color: 'black',
            height: rem(48),
            fontWeight: 500,
            '&:hover': {
              backgroundColor: '#BEE44A',
            },
          }}
        >
          <Group spacing="sm">
            <Text>Pay</Text>
            <Box
              sx={{
                width: rem(20),
                height: rem(20),
                borderRadius: '50%',
                backgroundColor: getCurrencyColor(formData.paymentMethod),
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                color: 'white',
                fontSize: rem(12),
                fontWeight: 600,
              }}
            >
              {getCurrencySymbol(formData.paymentMethod)}
            </Box>
            <Text weight={500}>
              {getPaymentAmount()}
              {' '}
              {formData.paymentMethod}
            </Text>
          </Group>
        </Button>

        {/* Footer text */}
        <Text size="xs" color="dimmed" align="center">
          Activate your card in 1 minute
        </Text>
        {/* Back button */}
        <Center>
          <Button
            variant="default"
            onClick={() => router.push('/flexi-card/apply')}
            sx={{ alignSelf: 'flex-start' }}
          >
            Back
          </Button>
        </Center>
      </Stack>
    </Box>
  );
}
