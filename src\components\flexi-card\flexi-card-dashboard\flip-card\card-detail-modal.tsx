/* eslint-disable complexity */
/* eslint-disable consistent-return */
/* eslint-disable sonarjs/no-duplicate-string */
/* eslint-disable max-lines */
import React, { useEffect, useState, useCallback } from 'react';
import {
  Modal,
  Box,
  Text,
  Alert,
  Stack,
  Flex,
  rem,
  useMantineTheme,
  ScrollArea,
  Badge,
} from '@mantine/core';
import {
  IconCheck,
  IconAlertCircle,
} from '@tabler/icons-react';
import useTranslation from 'next-translate/useTranslation';

type Props = {
  opened: boolean;
  onClose: () => void;
  card: {
    cardNumber: string;
    expiryDate: string;
    cardHolderName: string;
    billingAddress?: string;
    nativeCurrency?: string;
    phoneNumber?: string;
    email?: string;
  };
  // eslint-disable-next-line react/require-default-props
  revealSeconds?: number;
};

type CopyableFieldProps = {
  label: string;
  value: string;
  fieldKey: string;
  copiedField: string | null;
  onClick: () => void;
  // eslint-disable-next-line react/require-default-props
  monospace?: boolean;
};

function CopyableField({
  label,
  value,
  fieldKey,
  copiedField,
  onClick,
  monospace = false,
}: CopyableFieldProps) {
  const isCopied = copiedField === fieldKey;

  return (
    <Box
      p="sm"
      style={{
        borderRadius: 8,
        border: `1px solid ${isCopied ? '#339af0' : '#e9ecef'}`,
        cursor: 'pointer',
        transition: 'all 0.2s',
        transform: isCopied ? 'scale(1.02)' : 'scale(1)',
      }}
      onClick={onClick}
    >
      <Flex justify="space-between" align="center">
        <Text size="sm" color="dimmed">
          {label}
        </Text>
        <Flex align="center" gap="xs">
          <Text
            weight={600}
            style={monospace ? { fontFamily: 'monospace' } : undefined}
          >
            {value}
          </Text>
          {isCopied && (
            <Badge
              leftSection={<IconCheck size={12} />}
              variant="outline"
              color="blue"
              sx={{ height: 20, fontSize: '0.75rem' }}
            >
              Copied
            </Badge>
          )}
        </Flex>
      </Flex>
    </Box>
  );
}

export function CardCredentialsModal({
  opened,
  onClose,
  card,
  revealSeconds = 60,
}: Props) {
  const { t } = useTranslation();
  const [cvv] = useState('685');
  const [secondsLeft, setSecondsLeft] = useState(revealSeconds);
  const [copiedField, setCopiedField] = useState<string | null>(null);
  const theme = useMantineTheme();

  // sanitize card number
  const digits = (card?.cardNumber ?? '****************').replace(/\D/g, '');

  const handleCopyToClipboard = async (
    text: string,
    fieldName: string,
  ) => {
    try {
      await navigator.clipboard.writeText(text);
      setCopiedField(fieldName);
      setTimeout(() => {
        setCopiedField((current) => (current === fieldName ? null : current));
      }, 3000);
    } catch {
      // fail silently or show error toast
    }
  };

  const handleClose = useCallback(() => {
    setCopiedField(null);
    onClose();
  }, [onClose]);

  useEffect(() => {
    if (opened) {
      setCopiedField(null);
    }
  }, [opened]);

  useEffect(() => {
    if (!opened) return;
    const onVis = () => {
      if (document.hidden) handleClose();
    };
    document.addEventListener('visibilitychange', onVis);
    return () => {
      document.removeEventListener('visibilitychange', onVis);
    };
  }, [opened, handleClose]);

  useEffect(() => {
    if (!opened) return;
    setSecondsLeft(revealSeconds);
    const id = setInterval(
      () => setSecondsLeft((s) => (s <= 1 ? 0 : s - 1)),
      1000,
    );
    return () => clearInterval(id);
  }, [opened, revealSeconds]);

  useEffect(() => {
    if (opened && secondsLeft === 0) {
      handleClose();
    }
  }, [opened, secondsLeft, handleClose]);

  return (
    <Modal
      opened={opened}
      onClose={handleClose}
      title="Details/Credentials"
      centered
      scrollAreaComponent={ScrollArea.Autosize}
      radius="lg"
      size="md"
      styles={{
        close: {
          border: 'none',
          backgroundColor:
            theme.colorScheme === 'dark' ? theme.colors.dark[5] : '#f8f9fa',
          borderRadius: '50%',
          width: rem(32),
          height: rem(32),
          color: theme.colorScheme === 'dark' ? theme.white : '#495057',
          '&:hover': {
            backgroundColor:
              theme.colorScheme === 'dark' ? theme.colors.dark[4] : '#e9ecef',
          },
        },
      }}
    >
      <Box>
        <Alert
          icon={<IconAlertCircle size={16} />}
          // color="orange"
          mb="md"
          variant="filled"
          styles={{
            root: {
              backgroundColor:
          theme.colorScheme === 'dark'
            ? '#000000ff' // black, full opacity
            : '#ffffffff', // white, full opacity
              border: '1px solid #FFD43B',
              borderRadius: 18,
            },
            message: { color: '#E8590C' },
            icon: { color: '#E8590C' },
          }}
        >
          <Text size="sm" weight={600} style={{ color: '#E8590C' }}>
            {t('common:keepCardDetailsPrivate')}
          </Text>
        </Alert>

        {secondsLeft > 0 && (
          <Alert
            icon={<IconAlertCircle size={16} />}
            color="blue"
            mb="md"
            variant="light"
          >
            <Text size="sm">
              {t('common:windowAutoClose')}
              {' '}
              <strong>{secondsLeft}</strong>
              {' '}
              {t('common:seconds')}
            </Text>
          </Alert>
        )}

        <Box
          mb="md"
          p="md"
          style={{
            background: 'linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%)',
            borderTopLeftRadius: 18,
            borderTopRightRadius: 18,
            borderBottomLeftRadius: 0,
            borderBottomRightRadius: 0,
            color: 'white',
            position: 'relative',
            overflow: 'hidden',
            height: '120px',
          }}
        >
          <Text size="lg" weight={700} mb="xs">
            FLEXI
          </Text>
          <Text size="sm" style={{ color: '#ccc' }}>
            USD Zero
          </Text>
        </Box>

        <Text size="md" weight={600} mb="sm">
          {t('common:cardCredentials')}
        </Text>
        <Text size="sm" color="dimmed" mb="md">
          {t('common:tapToCopy')}
        </Text>

        <Stack spacing="xs">
          <CopyableField
            label={t('common:cardNumber')}
            value={digits}
            fieldKey="cardNumber"
            copiedField={copiedField}
            onClick={() => handleCopyToClipboard(digits, 'cardNumber')}
            monospace
          />
          <CopyableField
            label={t('common:nameOnCard')}
            value={card.cardHolderName}
            fieldKey="cardHolderName"
            copiedField={copiedField}
            onClick={() => handleCopyToClipboard(
              card.cardHolderName,
              'cardHolderName',
            )}
          />
          <CopyableField
            label={t('common:expiryDateMmYy')}
            value={card.expiryDate}
            fieldKey="expiryDate"
            copiedField={copiedField}
            onClick={() => handleCopyToClipboard(
              card.expiryDate,
              'expiryDate',
            )}
          />
          <CopyableField
            label={t('common:cvvCvc')}
            value={cvv}
            fieldKey="cvv"
            copiedField={copiedField}
            onClick={() => handleCopyToClipboard(cvv, 'cvv')}
          />
        </Stack>

        {/* Further Billing Information Section */}
        <Text size="md" weight={600} mt="lg" mb="sm">
          {t('common:furtherBillingInfo')}
        </Text>
        <Text size="sm" color="dimmed" mb="md">
          {t('common:tapToCopy')}
        </Text>

        <Stack spacing="xs">
          <CopyableField
            label={t('common:nativeCurrency')}
            value={card.nativeCurrency || 'USD'}
            fieldKey="nativeCurrency"
            copiedField={copiedField}
            onClick={() => handleCopyToClipboard(card.nativeCurrency || 'USD', 'nativeCurrency')}
          />
          <CopyableField
            label={t('common:phoneNumber')}
            value={card.phoneNumber || '(+999) 0999999999'}
            fieldKey="phoneNumber"
            copiedField={copiedField}
            onClick={() => handleCopyToClipboard(card.phoneNumber || '(+999) 0999999999', 'phoneNumber')}
          />
          <CopyableField
            label={t('common:email')}
            value={card.email || '<EMAIL>'}
            fieldKey="email"
            copiedField={copiedField}
            onClick={() => handleCopyToClipboard(card.email || '<EMAIL>', 'email')}
          />
        </Stack>
        <Text size="xs" color="dimmed" mt="md" style={{ lineHeight: 1.5 }}>
          {t('common:phoneEmailVerification')}
        </Text>
        <Text size="xs" color="dimmed" mt="xs" style={{ lineHeight: 1.5 }}>
          {t('common:billingAddressInfo')}
        </Text>
      </Box>
    </Modal>
  );
}
